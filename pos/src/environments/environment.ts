// This file can be replaced during build by using the `fileReplacements` array.
// `ng build` replaces `environment.ts` with `environment.prod.ts`.
// The list of file replacements can be found in `angular.json`.

export const environment = {
  production: false,
  baseUrl: 'https://oms-uat.rozana.tech/pos/',
  authBaseUrl: 'https://auth-uat.rozana.tech/',
  clientId: '7DUH8zDwkI7ITn9JHYlzNRmrQTOdqCK4SBZlzDUB',
  clientSecret: 'Ldb4cDLRb2EA2snyHAdqLiSg96A42oL25IqRwYrqMxuUrVWpl9JQiLlK2nCvlGklKXa8PRY4ChnmVmVyVV0Dtj2hoN5tfKHeBKIfGQaKxumYXBKxOyb0CTHaPbRk8L95',
  typesense: {
    host: 'tzr1vwndpi4qeg80p.a1.typesense.net',
    port: '443',
    protocol: 'https',
    apiKey: 'bhnrsYJntpVnVTVqWrK9gcPpQRzzmiNb',
  },
};

/*
 * For easier debugging in development mode, you can import the following file
 * to ignore zone related error stack frames such as `zone.run`, `zoneDelegate.invokeTask`.
 *
 * This import should be commented out in production mode because it will have a negative impact
 * on performance if an error is thrown.
 */
// import 'zone.js/plugins/zone-error';  // Included with Angular CLI.
