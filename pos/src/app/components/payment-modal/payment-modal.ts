import { ChangeDetectionStrategy, Component, EventEmitter, Input, Output, OnChanges, ChangeDetectorRef } from '@angular/core';
import { PaymentService, PaymentMethod, PaymentData, PaymentModalOutput } from '../../services/payment.config.service';
import { ConfirmationService } from 'primeng/api';
import { CustomerService } from '../../services/customer.service';
import { CommonService } from 'src/app/services/common.service';

@Component({
  selector: 'app-payment-modal',
  standalone: false,
  templateUrl: './payment-modal.html',
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class PaymentModalComponent implements OnChanges {
  @Input() visible = false;
  @Input() totalAmount = 0;
  @Input() isProcessing = false;
  @Input() title = 'Complete Payment';
  @Input() confirmButtonLabel = 'Confirm Payment';
  @Input() cancelButtonLabel = 'Cancel';
  @Output() visibleChange = new EventEmitter<boolean>();
  @Output() cancel = new EventEmitter<void>();
  @Output() confirm = new EventEmitter<PaymentModalOutput>();
  paymentData: PaymentData = { selectedPaymentMethod: 'cash' };
  remainingAmount = 0;
  change = 0;
  paymentMethods: PaymentMethod[] = [];
  customerDetails = { name: '', phone: '', create: false, customer_id: ''};

  constructor(
    private paymentService: PaymentService,
    private confirmationService: ConfirmationService,
    private customerService: CustomerService,
    private cdr: ChangeDetectorRef,
    private commonService: CommonService
  ) { }

  ionViewDidEnter(): void {
    this.customerDetails = { name: '', phone: '', create: false, customer_id: '' };
  }
  ngOnChanges(): void {
    if (this.totalAmount !== undefined) {
      this.updateAmounts();
      this.paymentMethods = this.paymentService.getPaymentMethods(this.totalAmount);
    }
    if (this.visible) this.resetPaymentData();
  }

  trackByMethod(_: number, m: PaymentMethod): string { return m.value; }

  getSelectedMethod(): PaymentMethod | undefined {
    return this.paymentMethods.find(m => m.value === this.paymentData.selectedPaymentMethod);
  }

  private resetPaymentData(): void {
    this.paymentData = this.paymentService.initializePaymentData(this.totalAmount);
    Object.assign(this, { 
      customerDetails: { name: '', phone: '', create: false, customer_id: '' },
      remainingAmount: this.totalAmount,
      change: 0
    });
    this.updateAmounts();
  }

  updateAmounts(): void {
    const baseAmount = this.totalAmount;
    const amounts = this.paymentService.calculateAmounts(this.paymentData, baseAmount);
    this.remainingAmount = amounts.remaining;
    this.change = amounts.change;
  }

  handlePaymentMethodChange(method: string): void {
    if (!this.isProcessing) {
      this.paymentData.selectedPaymentMethod = method;
      this.updateAmounts();
    }
  }

  onAmountFocus(input: HTMLInputElement, fieldKey: string): void {
    // Set the input value to '0' when focused
    input.value = '0';
    this.paymentData[fieldKey] = 0;
    this.updateAmounts();
  }

  onAmountInput(input: HTMLInputElement, fieldKey: string): void {
    const numValue = Number(input.value);
    if (!isNaN(numValue)) {
      this.paymentData[fieldKey] = numValue;
      this.updateAmounts();
    }
  }

  onCollectAmount(): void {
    if (this.isProcessing || !this.paymentData.cashAmount) return;
    
    const cashAmount = this.paymentData.cashAmount;
    
    if (cashAmount > 0) {
      if (cashAmount >= this.totalAmount) {
        // If paid in full or more, show change
        this.change = Math.max(0, cashAmount - this.totalAmount);
        this.remainingAmount = 0;
      } else {
        // If underpaid, update remaining amount
        this.remainingAmount = this.totalAmount - cashAmount;
      }
      this.cdr.detectChanges();
    }
  }

  onConfirm(): void {
    if (this.isProcessing) return;
    
    // If there's remaining amount and current method is cash, switch to card/UPI
    if (this.remainingAmount > 0 && this.paymentData.selectedPaymentMethod === 'cash') {
      // Store the cash amount paid
      const cashPaid = this.paymentData.cashAmount || 0;
      
      // Reset payment data for the next payment method
      this.paymentData = {
        selectedPaymentMethod: 'card',
        cardAmount: this.remainingAmount,
        paidAmount: cashPaid,
        remainingAmount: this.remainingAmount
      };
      
      // Update payment methods for the remaining amount
      this.paymentMethods = this.paymentService.getPaymentMethods(this.remainingAmount);
      
      // Force change detection
      this.cdr.detectChanges();
      return;
    }
    
    // Final validation and confirmation
    if (this.paymentService.validatePayment(this.paymentData, this.customerDetails.name, this.customerDetails.phone, this.totalAmount, false)) {
      const output: PaymentModalOutput = {
        paymentMethod: this.paymentData.selectedPaymentMethod,
        paymentData: this.paymentData,
        customerName: this.customerDetails.name,
        customerPhone: this.customerDetails.phone,
        customerId: this.customerDetails.customer_id
      };
      
      this.confirm.emit(output);
    }
  }

  onCancel(): void {
    if (!this.isProcessing) {
      this.visible = false;
      this.visibleChange.emit(false);
      this.cancel.emit();
    }
  }

  validatePhone(ev: any) {
    if (ev.target.value.length === 10) {
      this.customerDetails.phone = ev.target.value;
      this.customerService.verifyCustomer(ev.target.value).then((res: any) => {
        if (res?.data?.customer_name) {
          this.customerDetails.name = res.data.customer_name;
          this.customerDetails.customer_id = res.data.customer_id;
        } else {
          this.customerDetails.name = '';
          this.customerDetails.customer_id = '';
          this.customerDetails.create = true;
        }
        this.cdr.detectChanges();
      });
    } else {
      this.customerDetails.name = '';
      this.customerDetails.customer_id = '';
      this.customerDetails.create = false;
      this.cdr.detectChanges();
    }
  }

  createCustomer() {
    if (this.customerDetails.phone && this.customerDetails.name) {
      this.customerService.createCustomer({phone_number: this.customerDetails.phone, username: this.customerDetails.name}).then((res: any) => {
        this.commonService.setLoading(true);
        if (res.success) {
          this.customerDetails.customer_id = res.data.customer_id;
          this.cdr.detectChanges();
        } else {
          this.commonService.toast({ severity: 'error', summary: 'Error', detail: 'Failed to create customer' });
        }
      });
    } else {
      this.commonService.setLoading(true);
      this.commonService.toast({ severity: 'error', summary: 'Error', detail: 'Please enter phone number and name' });
    }
  }

  getConfirmButtonLabel(): string {
    if (this.isProcessing) return 'Processing...';
    
    if (this.remainingAmount > 0) {
      if (this.paymentData.selectedPaymentMethod === 'cash') {
        return `Pay Remaining ₹${this.remainingAmount.toFixed(2)}`;
      }
      return `Pay ₹${this.remainingAmount.toFixed(2)}`;
    }
    
    return this.confirmButtonLabel;
  }
}