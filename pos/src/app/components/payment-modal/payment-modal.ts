import { ChangeDetectionStrategy, Component, EventEmitter, Input, Output, OnChanges, ChangeDetectorRef } from '@angular/core';
import { PaymentService, PaymentMethod, PaymentData, PaymentModalOutput } from '../../services/payment.config.service';
import { ConfirmationService } from 'primeng/api';
import { CustomerService } from '../../services/customer.service';
import { CommonService } from 'src/app/services/common.service';

@Component({
  selector: 'app-payment-modal',
  standalone: false,
  templateUrl: './payment-modal.html',
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class PaymentModalComponent implements OnChanges {
  @Input() visible = false;
  @Input() totalAmount = 0;
  @Input() isProcessing = false;
  @Input() title = 'Complete Payment';
  @Input() confirmButtonLabel = 'Confirm Payment';
  @Input() cancelButtonLabel = 'Cancel';
  @Output() visibleChange = new EventEmitter<boolean>();
  @Output() cancel = new EventEmitter<void>();
  @Output() confirm = new EventEmitter<PaymentModalOutput>();
  paymentData: PaymentData = { selectedPaymentMethod: 'cash' };
  remainingAmount = 0;
  change = 0;
  paymentMethods: PaymentMethod[] = [];
  customerDetails = { name: '', phone: '', create: false, customer_id: ''};
  cashCollected = 0;
  isPartialPayment = false;

  constructor(
    private paymentService: PaymentService,
    private confirmationService: ConfirmationService,
    private customerService: CustomerService,
    private cdr: ChangeDetectorRef,
    private commonService: CommonService
  ) { }

  ionViewDidEnter(): void {
    this.customerDetails = { name: '', phone: '', create: false, customer_id: '' };
  }
  ngOnChanges(): void {
    if (this.totalAmount !== undefined) {
      this.updateAmounts();
      this.paymentMethods = this.paymentService.getPaymentMethods(this.totalAmount);
    }
    if (this.visible) this.resetPaymentData();
  }

  trackByMethod(_: number, m: PaymentMethod): string { return m.value; }

  getSelectedMethod(): PaymentMethod | undefined {
    return this.paymentMethods.find(m => m.value === this.paymentData.selectedPaymentMethod);
  }

  private resetPaymentData(): void {
    this.paymentData = this.paymentService.initializePaymentData(this.totalAmount);
    Object.assign(this, {
      customerDetails: { name: '', phone: '', create: false, customer_id: '' },
      remainingAmount: this.totalAmount,
      change: 0,
      cashCollected: 0,
      isPartialPayment: false
    });
    this.updateAmounts();
  }

  updateAmounts(): void {
    const baseAmount = this.totalAmount;
    const amounts = this.paymentService.calculateAmounts(this.paymentData, baseAmount);
    this.remainingAmount = amounts.remaining;
    this.change = amounts.change;
  }

  handlePaymentMethodChange(method: string): void {
    if (!this.isProcessing) {
      this.paymentData.selectedPaymentMethod = method;

      // If switching to online payment during partial payment, set the amount
      if (this.isPartialPayment && method !== 'cash') {
        const amountKey = `${method}Amount`;
        this.paymentData[amountKey] = this.remainingAmount;
      }

      this.updateAmounts();
      this.cdr.detectChanges();
    }
  }

  onAmountFocus(input: HTMLInputElement, fieldKey: string): void {
    // For partial payments, don't reset the amount if it's already set to remaining amount
    if (this.isPartialPayment && this.paymentData.selectedPaymentMethod !== 'cash') {
      const currentAmount = this.paymentData[fieldKey] || 0;
      if (Math.abs(currentAmount - this.remainingAmount) < 0.01) {
        return; // Don't reset if it's already set to the correct remaining amount
      }
    }

    // Set the input value to '0' when focused for new entries
    input.value = '0';
    this.paymentData[fieldKey] = 0;
    this.updateAmounts();
  }

  onAmountInput(input: HTMLInputElement, fieldKey: string): void {
    const numValue = Number(input.value);
    if (!isNaN(numValue)) {
      this.paymentData[fieldKey] = numValue;
      this.updateAmounts();
    }
  }

  onCollectAmount(): void {
    if (this.isProcessing || !this.paymentData.cashAmount) return;

    const cashAmount = this.paymentData.cashAmount;

    if (cashAmount > 0) {
      // Store the collected cash amount
      this.cashCollected = cashAmount;

      if (cashAmount >= this.totalAmount) {
        // If paid in full or more, show change and allow proceeding to payment
        this.change = Math.max(0, cashAmount - this.totalAmount);
        this.remainingAmount = 0;
        this.isPartialPayment = false;
      } else {
        // If underpaid, mark as partial payment and calculate remaining
        this.remainingAmount = this.totalAmount - cashAmount;
        this.change = 0;
        this.isPartialPayment = true;
      }
      this.cdr.detectChanges();
    }
  }

  onConfirm(): void {
    if (this.isProcessing) return;

    // Case 1: Cash payment with exact or more amount - proceed to payment
    if (this.paymentData.selectedPaymentMethod === 'cash' && this.remainingAmount === 0) {
      this.proceedToPayment();
      return;
    }

    // Case 2: Cash payment with less amount - need to collect remaining via UPI/Card
    if (this.isPartialPayment && this.paymentData.selectedPaymentMethod === 'cash') {
      // Switch to card/UPI for remaining amount
      this.switchToOnlinePayment();
      return;
    }

    // Case 3: UPI/Card payment for remaining amount
    if (this.isPartialPayment && this.paymentData.selectedPaymentMethod !== 'cash') {
      this.proceedToMixedPayment();
      return;
    }

    // Case 4: Full UPI/Card payment
    if (!this.isPartialPayment && this.paymentData.selectedPaymentMethod !== 'cash') {
      this.proceedToPayment();
      return;
    }
  }

  private switchToOnlinePayment(): void {
    // Filter out cash payment method for remaining amount
    this.paymentMethods = this.paymentService.getPaymentMethods(this.remainingAmount)
      .filter(method => method.value !== 'cash');

    // Switch to card payment for remaining amount (default to card if available)
    const preferredMethod = this.paymentMethods.find(m => m.value === 'card') || this.paymentMethods[0];
    if (preferredMethod) {
      // Clear previous payment data except cash amount
      const cashAmount = this.paymentData.cashAmount;
      this.paymentData = {
        selectedPaymentMethod: preferredMethod.value,
        cashAmount: cashAmount // Keep the cash amount for reference
      };

      // Set the amount for the selected online payment method
      const amountKey = `${preferredMethod.value}Amount`;
      this.paymentData[amountKey] = this.remainingAmount;
    }

    this.cdr.detectChanges();
  }

  private proceedToPayment(): void {
    // Validate and proceed with single payment method
    const validationAmount = this.isPartialPayment ? this.remainingAmount : this.totalAmount;

    if (this.paymentService.validatePayment(this.paymentData, this.customerDetails.name, this.customerDetails.phone, validationAmount, this.isPartialPayment)) {
      const output: PaymentModalOutput = {
        paymentMethod: this.paymentData.selectedPaymentMethod,
        paymentData: this.paymentData,
        customerName: this.customerDetails.name,
        customerPhone: this.customerDetails.phone,
        customerId: this.customerDetails.customer_id
      };

      this.confirm.emit(output);
    }
  }

  private proceedToMixedPayment(): void {
    // Validate the online payment for remaining amount
    if (this.paymentService.validatePayment(this.paymentData, this.customerDetails.name, this.customerDetails.phone, this.remainingAmount, true)) {
      // Create mixed payment data
      const multiPaymentData = {
        cashAmount: this.cashCollected,
        onlineAmount: this.remainingAmount,
        onlineMethod: this.paymentData.selectedPaymentMethod as 'upi' | 'card',
        upiId: this.paymentData.upiId,
        cardType: this.paymentData.cardType
      };

      const output: PaymentModalOutput = {
        paymentMethod: 'mixed',
        paymentData: this.paymentData,
        multiPaymentData: multiPaymentData,
        customerName: this.customerDetails.name,
        customerPhone: this.customerDetails.phone,
        customerId: this.customerDetails.customer_id
      };

      this.confirm.emit(output);
    }
  }

  onCancel(): void {
    if (!this.isProcessing) {
      this.visible = false;
      this.visibleChange.emit(false);
      this.cancel.emit();
    }
  }

  validatePhone(ev: any) {
    if (ev.target.value.length === 10) {
      this.customerDetails.phone = ev.target.value;
      this.customerService.verifyCustomer(ev.target.value).then((res: any) => {
        if (res?.data?.customer_name) {
          this.customerDetails.name = res.data.customer_name;
          this.customerDetails.customer_id = res.data.customer_id;
        } else {
          this.customerDetails.name = '';
          this.customerDetails.customer_id = '';
          this.customerDetails.create = true;
        }
        this.cdr.detectChanges();
      });
    } else {
      this.customerDetails.name = '';
      this.customerDetails.customer_id = '';
      this.customerDetails.create = false;
      this.cdr.detectChanges();
    }
  }

  createCustomer() {
    if (this.customerDetails.phone && this.customerDetails.name) {
      this.customerService.createCustomer({phone_number: this.customerDetails.phone, username: this.customerDetails.name}).then((res: any) => {
        this.commonService.setLoading(true);
        if (res.success) {
          this.customerDetails.customer_id = res.data.customer_id;
          this.cdr.detectChanges();
        } else {
          this.commonService.toast({ severity: 'error', summary: 'Error', detail: 'Failed to create customer' });
        }
      });
    } else {
      this.commonService.setLoading(true);
      this.commonService.toast({ severity: 'error', summary: 'Error', detail: 'Please enter phone number and name' });
    }
  }

  getConfirmButtonLabel(): string {
    if (this.isProcessing) return 'Processing...';

    // If cash is collected and matches total amount
    if (this.paymentData.selectedPaymentMethod === 'cash' && this.remainingAmount === 0) {
      return 'Proceed to Pay';
    }

    // If cash is collected but there's remaining amount
    if (this.isPartialPayment && this.paymentData.selectedPaymentMethod === 'cash') {
      return `Pay Remaining ₹${this.remainingAmount.toFixed(2)}`;
    }

    // If paying remaining amount via UPI/Card
    if (this.isPartialPayment && this.paymentData.selectedPaymentMethod !== 'cash') {
      return `Pay ₹${this.remainingAmount.toFixed(2)}`;
    }

    // Default case
    return this.confirmButtonLabel;
  }

  shouldDisableConfirmButton(): boolean {
    if (this.isProcessing) return true;

    // If cash payment and amount collected matches total
    if (this.paymentData.selectedPaymentMethod === 'cash' && this.remainingAmount === 0) {
      return false;
    }

    // If partial payment and switching to online payment
    if (this.isPartialPayment && this.paymentData.selectedPaymentMethod !== 'cash') {
      const amountKey = `${this.paymentData.selectedPaymentMethod}Amount`;
      const onlineAmount = this.paymentData[amountKey] || 0;

      // Check if required fields are filled for the payment method
      const method = this.getSelectedMethod();
      if (method) {
        for (const field of method.fields) {
          if (field.required && !this.paymentData[field.key]?.toString().trim()) {
            return true; // Required field is missing
          }
        }
      }

      // Check if amount matches remaining amount
      return Math.abs(onlineAmount - this.remainingAmount) > 0.01;
    }

    // If cash payment but amount not collected yet
    if (this.paymentData.selectedPaymentMethod === 'cash' && this.cashCollected === 0) {
      return true;
    }

    // For full online payments (not partial)
    if (!this.isPartialPayment && this.paymentData.selectedPaymentMethod !== 'cash') {
      const method = this.getSelectedMethod();
      if (method) {
        for (const field of method.fields) {
          if (field.required && !this.paymentData[field.key]?.toString().trim()) {
            return true;
          }
        }
      }
      return false;
    }

    // Default case - check if remaining amount exists
    return this.remainingAmount > 0;
  }
}