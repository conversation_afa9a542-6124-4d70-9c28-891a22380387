<p-dialog appendTo="body" [modal]="true" [(visible)]="visible" [style]="{width: '50vw', 'max-width':'800px'}"
  [breakpoints]="{'960px': '75vw', '640px': '95vw'}" [closable]="false" [draggable]="false" [resizable]="false">
  <ng-template pTemplate="header">
    <div class="flex justify-between w-full">
      <h5>{{title}}</h5>
    </div>
  </ng-template>
  <div class="flex flex-col text-md">
    <!-- Customer Information -->
    <div class="w-full mb-2 mt-2">
      <h6 class="mb-2 leading-none font-semibold text-gray-800">Customer Information</h6>
      <div class="flex gap-4 mt-3">
        <input pInputText id="customerPhone" placeholder="Phone Number" [(ngModel)]="customerDetails.phone"
          [disabled]="isProcessing" autocomplete="off" class="w-full cutom-input" minlength="10" maxlength="10"
          type="tel" (keyup)="validatePhone($event)" />
        <input pInputText id="customerName" placeholder="Customer Name" [(ngModel)]="customerDetails.name"
          [disabled]="isProcessing" autocomplete="off" class="w-full cutom-input" />
        <button [appDisableDoubleClick]="true" class="w-[100px]" *ngIf="customerDetails.create" pButton type="button" label="Create"
          (click)="createCustomer()" [disabled]="isProcessing" size="small"></button>
      </div>
    </div>
    <!-- Payment Methods -->
    <div class="w-full mt-2">
      <h6 class="mb-3 leading-none font-semibold text-gray-800">
        {{ isPartialPayment && paymentData.selectedPaymentMethod !== 'cash' ? 'Pay Remaining Amount via UPI/Card' : 'Select Payment Method' }}
      </h6>
      <div class="grid grid-cols-2 gap-3 mb-6 md:grid-cols-3">
        <div *ngFor="let method of paymentMethods; trackBy: trackByMethod"
          (click)="handlePaymentMethodChange(method.value)"
          class="p-3 transition-all duration-200 border-2 rounded-lg cursor-pointer hover:shadow-md" 
          [class.border-blue-500]="paymentData.selectedPaymentMethod === method.value"
          [class.bg-blue-50]="paymentData.selectedPaymentMethod === method.value"
          [class.border-gray-200]="paymentData.selectedPaymentMethod !== method.value"
          [class.hover:border-blue-300]="paymentData.selectedPaymentMethod !== method.value"
          [class.opacity-60]="isProcessing">
          <div class="flex align-center items-center text-xl justify-center">
            <i [class]="method.icon + ' text-lg mr-2'"
              [ngClass]="paymentData.selectedPaymentMethod === method.value ? 'text-blue-600' : 'text-gray-600'"></i>
            <span class="text-sm font-medium text-center"
              [ngClass]="paymentData.selectedPaymentMethod === method.value ? 'text-blue-700' : 'text-gray-700'">
              {{method.label}}
            </span>
          </div>
        </div>
      </div>

      <!-- Payment Details -->
      <div class="mb-6" *ngIf="getSelectedMethod() as method">
        <div class="p-3 border rounded-lg">
          <div class="flex items-center gap-3 mb-4">
            <i [class]="method.icon + ' '"></i>
            <h6 class="font-semibold leading-none">
              {{method.label}} Payment
            </h6>
          </div>

          <div class="space-y-4">
            <div *ngFor="let field of method.fields" class="mb-3">
              <!-- Amount Field -->
              <ng-container *ngIf="field.type === 'amount'">
                <div class="flex items-center gap-2">
                  <div class="flex-1">
                    <label class="block mb-1 text-sm font-medium text-gray-700">{{field.label}}</label>
                    <input placeholder="Enter amount" pInputText #amountInput type="number"
                      [value]="paymentData[field.key] || ''" 
                      (input)="onAmountInput(amountInput, field.key)"
                      (focus)="onAmountFocus(amountInput, field.key)"
                      [min]="field.min" [max]="field.max"
                      [disabled]="isProcessing"
                      class="w-full p-2 border rounded" step="0.01" autocomplete="off">
                  </div>
                  <button *ngIf="field.key === 'cashAmount'" pButton pRipple type="button"
                    [label]="cashCollected > 0 ? 'Amount Collected' : 'Collect Amount'"
                    (click)="onCollectAmount()"
                    [disabled]="isProcessing || !paymentData.cashAmount || paymentData.cashAmount <= 0 || cashCollected > 0"
                    class="mt-6"
                    [ngClass]="{
                      'opacity-50 cursor-not-allowed': isProcessing || !paymentData.cashAmount || paymentData.cashAmount <= 0,
                      'p-button-success': cashCollected > 0,
                      'p-button-outlined': cashCollected === 0
                    }">
                  </button>
                </div>
                <small *ngIf="cashCollected > 0 && cashCollected < totalAmount" class="text-blue-600 block mt-1">
                  <i class="pi pi-check-circle mr-1"></i>
                  Amount collected: ₹{{ cashCollected.toFixed(2) }}. Remaining: ₹{{ (totalAmount - cashCollected).toFixed(2) }}
                </small>
                <small *ngIf="cashCollected > 0 && cashCollected >= totalAmount" class="text-green-600 block mt-1">
                  <i class="pi pi-check-circle mr-1"></i>
                  Amount collected: ₹{{ cashCollected.toFixed(2) }}.
                  <span *ngIf="change > 0">Return: ₹{{ change.toFixed(2) }}</span>
                </small>
              </ng-container>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- Order Summary -->
    <div class="p-4 border border-gray-200 rounded-lg bg-gray-50">
      <div class="grid grid-cols-1 gap-2 text-2xl">
        <div class="flex justify-between">
          <span class="font-bold text-gray-800">Total Amount:</span>
          <span class="font-bold">{{ totalAmount | currency:'INR' }}</span>
        </div>

        <div *ngIf="cashCollected > 0" class="flex justify-between text-lg text-blue-600">
          <span>Cash Collected:</span>
          <span>{{ cashCollected | currency:'INR' }}</span>
        </div>

        <div class="flex justify-between text-lg text-gray-600" *ngIf='remainingAmount > 0'>
          <span>Remaining Amount:</span>
          <span>{{ remainingAmount | currency:'INR' }}</span>
        </div>

        <div *ngIf="change > 0" class="flex justify-between text-lg text-green-600">
          <span>Return Amount:</span>
          <span>{{ change | currency:'INR' }}</span>
        </div>
      </div>
    </div>
  </div>

  <!-- Footer Buttons -->
  <ng-template pTemplate="footer">
    <div class="flex justify-end w-full gap-2 mt-2">
      <button pButton pRipple [severity]="'danger'" type="button" [label]="cancelButtonLabel" [disabled]="isProcessing"
        (click)="onCancel()"></button>
      <button pButton pRipple type="button" [label]="getConfirmButtonLabel()"
        [disabled]="shouldDisableConfirmButton()" [loading]="isProcessing"
        (click)="onConfirm()"></button>
    </div>
  </ng-template>
</p-dialog>