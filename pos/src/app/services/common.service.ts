import { HttpClient, HttpErrorResponse, HttpHeaders } from "@angular/common/http";
import { EventEmitter, Injectable, OnDestroy } from "@angular/core";
import { MessageService } from "primeng/api";
import { Observable, Subject, throwError } from "rxjs";
import { catchError, finalize, takeUntil } from "rxjs/operators";
import { environment } from "src/environments/environment";
import { StorageService } from "./storage.service";
import { Facility } from "../models";

@Injectable({
  providedIn: 'root'
})
export class CommonService implements OnDestroy {
  private destroy$ = new Subject<void>();
  private loading = false;
  public currentFacility: Facility | undefined;
  disableBtn: EventEmitter<boolean> = new EventEmitter<boolean>();
  constructor(
    private http: HttpClient,
    private message: MessageService,
    private storageService: StorageService
  ) { }

  getBaseUrl(options: any = {}){
    if (options.authBaseUrl) {
      return environment.authBaseUrl;
    }
    return environment.baseUrl;
  }
  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }
  setDisableBtn(state: boolean = false): void {
    setTimeout(() => {
      this.disableBtn.emit(state);
    }, 1500)
  }
  toast(data: { severity: string, summary: string, detail: string }): void {
    this.message.add(data);
  }

  isLoading(): boolean {
    return this.loading;
  }

  setLoading(state: boolean): void {
    this.loading = state;
  }

  getCommonHeaders(options: any = {}): HttpHeaders {
    let headers = new HttpHeaders();
    const token = this.storageService.getToken();
    if (options.authBaseUrl && token) {
      const authToken = this.storageService.getAuthToken();
      if (authToken) {
        headers = headers.set('Authorization', `${authToken}`);
      }
    } else if (token) {
      headers = headers.set('Authorization', `${token}`);
    }
    return headers;
  }

  private handleError(error: HttpErrorResponse): Observable<never> {
    return throwError(() => error.error);
  }

  get<T>(endpoint: string, params?: any, extras: any = {}): Observable<T> {
    this.setLoading(true);
    const options = {
      headers: this.getCommonHeaders(extras),
      params: params
    };
    return this.http.get<T>(`${this.getBaseUrl(extras)}${endpoint}`, options).pipe(
      takeUntil(this.destroy$),
      catchError(error => this.handleError(error)),
      finalize(() => this.setLoading(false))
    );
  }

  post<T>(endpoint: string, body: any, extras: any = {}): Observable<T> {
    this.setLoading(true);
    const options = {
      headers: this.getCommonHeaders(extras)
    };
    const url = `${this.getBaseUrl(extras)}${endpoint}`
    return this.http.post<T>(url, body, options).pipe(
      takeUntil(this.destroy$),
      catchError(error => this.handleError(error)),
      finalize(() => this.setLoading(false))
    );
  }

  put<T>(endpoint: string, body: any, extras: any = {}): Observable<T> {
    this.setLoading(true);
    const options = {
      headers: this.getCommonHeaders(extras)
    };

    return this.http.put<T>(`${this.getBaseUrl(extras)}${endpoint}`, body, options).pipe(
      takeUntil(this.destroy$),
      catchError(error => this.handleError(error)),
      finalize(() => this.setLoading(false))
    );
  }

  delete<T>(endpoint: string, extras: any = {}): Observable<T> {
    this.setLoading(true);
    const options = {
      headers: this.getCommonHeaders(extras)
    };

    return this.http.delete<T>(`${this.getBaseUrl(extras)}${endpoint}`, options).pipe(
      takeUntil(this.destroy$),
      catchError(error => this.handleError(error)),
      finalize(() => this.setLoading(false))
    );
  }
}