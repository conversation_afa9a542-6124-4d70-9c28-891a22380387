import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IonContent } from '@ionic/angular/standalone';
import { ButtonModule } from 'primeng/button';
import { FormsModule } from '@angular/forms';
import { CommonService } from 'src/app/services/common.service';
import { Router } from '@angular/router';
import { InputNumberModule } from 'primeng/inputnumber';
import { IftaLabel } from "primeng/iftalabel";
import { InputTextModule } from 'primeng/inputtext';
import { RippleModule } from 'primeng/ripple';
import { FirebaseAuthService } from 'src/app/services/firebase-auth.service';
import { StorageService } from 'src/app/services/storage.service';
import { FirestoreService } from 'src/app/firebase/firestore.service';
import { environment } from 'src/environments/environment';
import { lastValueFrom } from 'rxjs';
@Component({
  selector: 'app-login',
  standalone: true,
  templateUrl: './login.page.html',
  imports: [
    CommonModule,
    IonContent,
    InputNumberModule,
    ButtonModule,
    FormsModule,
    IftaLabel,
    InputTextModule,
    RippleModule
],
})
export class LoginPage implements OnInit {
  phoneNumber: number | null = null;
  otpCode: string = '';
  showOtpInput: boolean = false;
  isLoading: boolean = false;
  isVerifying: boolean = false;
  
  constructor(
    private commonService: CommonService,
    private router: Router,
    private firebaseAuthService: FirebaseAuthService,
    private storage: StorageService,
    private firestoreService: FirestoreService
  ) {}
  ionViewDidEnter() {
    this.showOtpInput = false;
    this.phoneNumber = null;
    this.isVerifying = false;
    this.isLoading = false;
    this.otpCode = '';
  }
  async ngOnInit() {
    // Check if user is already logged in
    const user = await this.firebaseAuthService.getCurrentUser();
    if (user) {
      this.router.navigate(['/pages/home']);
    }
  }

  async onLogin() {
    if (!this.phoneNumber || this.phoneNumber.toString().length < 10) {
      this.commonService.toast({severity: 'error', summary: 'Error', detail: 'Please enter valid phone number'});
      return;
    }

    try {
      this.isLoading = true;
      const success = await this.firebaseAuthService.sendOTP(
        this.phoneNumber.toString(),
        'recaptcha-container'
      );

      if (success) {
        this.showOtpInput = true;
        this.commonService.toast({severity: 'info', summary: 'OTP Sent', detail: 'Please check your phone for the OTP code'});
      } else {
        this.commonService.toast({severity: 'error', summary: 'Error', detail: 'Failed to send OTP. Please try again.'});
      }
    } catch (error) {
      console.error('Error sending OTP:', error);
      this.commonService.toast({severity: 'error', summary: 'Error', detail: 'An error occurred. Please try again.'});
    } finally {
      this.isLoading = false;
    }
  }

  async verifyOtp() {
    if (!this.otpCode || this.otpCode.trim() === '') {
      this.commonService.toast({severity: 'error', summary: 'Error', detail: 'Please enter the OTP code'});
      return;
    }

    try {
      this.isVerifying = true;
      const result = await this.firebaseAuthService.verifyOTP(this.otpCode);
      if (result.success) {
        await this.firestoreService.getCurrentUserFields();
        const formData = new FormData();
        formData.append('client_id', environment.clientId);
        formData.append('client_secret', environment.clientSecret);
        formData.append('grant_type', 'client_credentials');
        const authToken: any = await lastValueFrom(this.commonService.post('o/token/', formData, {authBaseUrl: true, formData: true}))
        if (authToken.data?.access_token){
          this.storage.setItem('authToken', authToken.data?.access_token);
          this.storage.setItem('user', {...result.user?.providerData?.[0], uid: result.user?.uid});
          this.storage.setItem('token', result.user?.accessToken);
          this.commonService.toast({severity: 'success', summary: 'Success', detail: 'Login successful!'});
          this.router.navigate(['/pages/home']);
        } else {
          this.commonService.toast({severity: 'error', summary: 'Error', detail: 'Failed to get auth token. Please try again.'});
        }
      } else {
        this.commonService.toast({severity: 'error', summary: 'Error', detail: 'Invalid OTP. Please try again.'});
      }
    } catch (error) {
      console.error('Error verifying OTP:', error);
      this.commonService.toast({severity: 'error', summary: 'Error', detail: 'An error occurred. Please try again.'});
    } finally {
      this.isVerifying = false;
    }
  }

  resendOtp() {
    this.otpCode = '';
    this.onLogin();
  }

  changePhoneNumber() {
    this.showOtpInput = false;
    this.otpCode = '';
  }
}
